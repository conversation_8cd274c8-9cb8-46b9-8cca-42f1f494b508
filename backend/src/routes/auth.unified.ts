import express from 'express';
import { body } from 'express-validator';
import {
  adminLogin,
  adminLogout,
  getCurrentAdmin,
  verifyAuth,
  loginLimiter
} from '../controllers/auth.controller.unified';
import { 
  authenticate, 
  requireAdmin, 
  requireMainAdmin 
} from '../middleware/auth.unified';

const router = express.Router();

/**
 * @route POST /api/auth/admin/login
 * @desc Admin login with HTTP-only cookies
 * @access Public
 */
router.post(
  '/admin/login',
  loginLimiter,
  [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Please enter a valid email address'),
    body('password')
      .isLength({ min: 1 })
      .withMessage('Password is required'),
  ],
  adminLogin
);

/**
 * @route POST /api/auth/admin/logout
 * @desc Admin logout
 * @access Public
 */
router.post('/admin/logout', adminLogout);

/**
 * @route GET /api/auth/admin/profile
 * @desc Get current admin profile
 * @access Private (Admin only)
 */
router.get('/admin/profile', requireAdmin, getCurrentAdmin);

/**
 * @route GET /api/auth/verify
 * @desc Verify authentication status
 * @access Private
 */
router.get('/verify', authenticate, verifyAuth);

/**
 * @route GET /api/auth/admin/verify
 * @desc Verify admin authentication status
 * @access Private (Admin only)
 */
router.get('/admin/verify', requireAdmin, verifyAuth);

/**
 * @route GET /api/auth/debug
 * @desc Debug authentication state
 * @access Public
 */
router.get('/debug', (req, res) => {
  const token = req.cookies.auth_token;
  res.json({
    success: true,
    debug: {
      hasCookie: !!token,
      cookieValue: token ? 'present' : 'missing',
      cookies: Object.keys(req.cookies),
      headers: {
        authorization: req.headers.authorization,
        cookie: req.headers.cookie
      }
    }
  });
});

/**
 * @route POST /api/auth/clear-cookies
 * @desc Clear all authentication cookies (for debugging)
 * @access Public
 */
router.post('/clear-cookies', (req, res) => {
  // Clear all possible authentication cookies
  res.clearCookie('auth_token', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
    path: '/'
  });

  // Clear any other potential cookies
  res.clearCookie('token');
  res.clearCookie('admin_token');
  res.clearCookie('session');

  res.json({
    success: true,
    message: 'All authentication cookies cleared'
  });
});

/**
 * @route POST /api/auth/reset-admin-account
 * @desc Reset admin account (clear failed attempts and unlock)
 * @access Public (for debugging only)
 */
router.post('/reset-admin-account', async (req, res) => {
  try {
    const { email } = req.body;
    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email is required'
      });
    }

    // Import Admin model
    const { Admin } = await import('../models/Admin');

    // Find admin by email first
    const admin = await Admin.findByEmail(email);
    if (!admin) {
      return res.status(404).json({
        success: false,
        message: 'Admin not found'
      });
    }

    // Reset failed login attempts and unlock account
    await Admin.updateFailedLoginAttempts(
      admin.id!,
      0,
      undefined // Clear lock_until
    );

    res.json({
      success: true,
      message: 'Admin account reset successfully',
      data: {
        email,
        id: admin.id,
        previousAttempts: admin.failedLoginAttempts,
        wasLocked: !!admin.lockUntil,
        reset: true
      }
    });

  } catch (error) {
    console.error('Reset admin account error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reset admin account',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

export default router;
