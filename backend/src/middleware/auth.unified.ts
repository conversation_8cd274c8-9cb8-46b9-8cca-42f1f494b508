import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { Admin } from '../models/Admin';
import { JWTUtils, JWT_CONFIG } from '../config/jwt.config';

/**
 * UNIFIED AUTHENTICATION MIDDLEWARE
 * Industry-standard implementation with HTTP-only cookies
 */

interface AuthenticatedRequest extends Request {
  user?: {
    id: number;
    email: string;
    role: string;
    isMainAdmin: boolean;
  };
}

/**
 * Main authentication middleware
 * Verifies JWT token from HTTP-only cookie
 */
export const authenticate = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    // Get token from HTTP-only cookie using centralized configuration
    const token = req.cookies[JWT_CONFIG.COOKIE_NAME];

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        error: 'No authentication token found',
        code: 'NO_TOKEN'
      });
    }

    // Verify JWT token using centralized configuration
    const decoded = JWTUtils.verifyToken(token);

    // Validate token structure
    if (!decoded.id || !decoded.email || !decoded.role) {
      res.clearCookie('auth_token');
      return res.status(401).json({
        success: false,
        message: 'Invalid token structure',
        error: 'Authentication token is malformed',
        code: 'INVALID_TOKEN'
      });
    }

    // Verify admin still exists in database
    const admin = await Admin.findById(decoded.id);
    if (!admin) {
      res.clearCookie('auth_token');
      return res.status(401).json({
        success: false,
        message: 'Authentication failed',
        error: 'Admin account not found',
        code: 'ADMIN_NOT_FOUND'
      });
    }

    // Check if account is locked
    if (admin.lockUntil && admin.lockUntil > new Date()) {
      return res.status(423).json({
        success: false,
        message: 'Account is locked',
        error: 'Please contact support',
        code: 'ACCOUNT_LOCKED'
      });
    }

    // Attach user info to request
    req.user = {
      id: decoded.id,
      email: decoded.email,
      role: decoded.role,
      isMainAdmin: decoded.isMainAdmin || false
    };

    next();

  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      // Clear invalid token using centralized configuration
      res.clearCookie(JWT_CONFIG.COOKIE_NAME);
      return res.status(401).json({
        success: false,
        message: 'Authentication failed',
        error: 'Invalid or expired token',
        code: 'TOKEN_INVALID'
      });
    }

    console.error('Authentication error:', error);
    res.status(500).json({
      success: false,
      message: 'Authentication error',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
};

/**
 * Admin authorization middleware
 * Requires user to be authenticated as an admin
 */
export const requireAdmin = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    // First authenticate
    await authenticate(req, res, () => {
      // Check if user is an admin
      if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'super_admin' && !req.user.isMainAdmin)) {
        return res.status(403).json({
          success: false,
          message: 'Access denied',
          error: 'Admin privileges required',
          code: 'INSUFFICIENT_PRIVILEGES'
        });
      }
      next();
    });
  } catch (error) {
    console.error('Admin authorization error:', error);
    res.status(401).json({
      success: false,
      message: 'Authentication required',
      error: 'Please authenticate as an admin'
    });
  }
};

/**
 * Main admin authorization middleware
 * Requires user to be authenticated as the main admin
 */
export const requireMainAdmin = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    // First authenticate
    await authenticate(req, res, () => {
      // Check if user is the main admin
      if (!req.user || !req.user.isMainAdmin) {
        return res.status(403).json({
          success: false,
          message: 'Access denied',
          error: 'Main admin privileges required',
          code: 'MAIN_ADMIN_REQUIRED'
        });
      }
      next();
    });
  } catch (error) {
    console.error('Main admin authorization error:', error);
    res.status(401).json({
      success: false,
      message: 'Authentication required',
      error: 'Please authenticate as the main admin'
    });
  }
};

/**
 * Optional authentication middleware
 * Adds user info to request if authenticated, but doesn't require authentication
 */
export const optionalAuth = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const token = req.cookies.auth_token;

    if (token) {
      try {
        // Use centralized JWT verification
        const decoded = JWTUtils.verifyToken(token);

        const admin = await Admin.findById(decoded.id);
        if (admin && (!admin.lockUntil || admin.lockUntil <= new Date())) {
          req.user = {
            id: decoded.id,
            email: decoded.email,
            role: decoded.role,
            isMainAdmin: decoded.isMainAdmin || false
          };
        }
      } catch (error) {
        // Ignore authentication errors for optional auth
        console.log('Optional auth failed (this is normal):', (error as Error).message);
      }
    }

    next();
  } catch (error) {
    // Continue without authentication for optional auth
    next();
  }
};

export default {
  authenticate,
  requireAdmin,
  requireMainAdmin,
  optionalAuth
};
