import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { Admin } from '../models/Admin';
import { JWTUtils, JWT_CONFIG } from '../config/jwt.config';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: number;
        email: string;
        role: string;
        isMainAdmin: boolean;
      };
    }
  }
}

/**
 * Authentication middleware using HTTP-only cookies
 */
export const authenticate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Get token from HTTP-only cookie using centralized configuration
    const token = req.cookies[JWT_CONFIG.COOKIE_NAME];

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        error: 'No authentication token found'
      });
    }

    // PROFESSIONAL JWT VERIFICATION using centralized configuration
    let decoded: any;
    try {
      decoded = JWTUtils.verifyToken(token);
    } catch (jwtError) {
      res.clearCookie(JWT_CONFIG.COOKIE_NAME);
      return res.status(401).json({
        success: false,
        message: 'Authentication failed',
        error: 'Invalid or expired token'
      });
    }

    // Additional security checks
    if (!decoded.id || !decoded.email || !decoded.role) {
      res.clearCookie('auth_token');
      return res.status(401).json({
        success: false,
        message: 'Invalid token structure',
        error: 'Authentication token is malformed'
      });
    }

    // Verify admin still exists in database
    const admin = await Admin.findById(decoded.id);
    if (!admin) {
      // Clear invalid cookie
      res.clearCookie('auth_token');
      return res.status(401).json({
        success: false,
        message: 'Authentication failed',
        error: 'Admin account not found'
      });
    }

    // Check if account is locked
    if (admin.lockUntil && admin.lockUntil > new Date()) {
      return res.status(423).json({
        success: false,
        message: 'Account is locked',
        error: 'Please contact support'
      });
    }

    // Attach user info to request
    req.user = {
      id: decoded.id,
      email: decoded.email,
      role: decoded.role,
      isMainAdmin: decoded.isMainAdmin || false
    };

    // Check if token is about to expire (less than 2 hours remaining)
    const tokenExp = decoded.exp;
    const currentTime = Math.floor(Date.now() / 1000);
    const twoHours = 2 * 60 * 60; // 2 hours in seconds

    if (tokenExp && tokenExp - currentTime < twoHours) {
      // Generate new token
      const newToken = jwt.sign(
        {
          id: admin.id,
          email: admin.email,
          role: admin.role,
          isMainAdmin: admin.isMainAdmin
        },
        process.env.JWT_SECRET || 'fallback-secret-key',
        {
          expiresIn: '2h',
          issuer: process.env.JWT_ISSUER || 'mabourse-api',
          audience: process.env.JWT_AUDIENCE || 'mabourse-admin'
        }
      );

      // Set new cookie
      res.cookie('auth_token', newToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax',
        maxAge: 2 * 60 * 60 * 1000, // 2 hours (matches JWT expiration)
        path: '/'
      });
    }

    next();

  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      // Clear invalid cookie
      res.clearCookie('auth_token');
      return res.status(401).json({
        success: false,
        message: 'Authentication failed',
        error: 'Invalid or expired token'
      });
    }

    console.error('Authentication middleware error:', error);
    res.status(500).json({
      success: false,
      message: 'Authentication error',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
};

/**
 * Admin authorization middleware
 * Requires user to be authenticated as an admin
 */
export const requireAdmin = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // First authenticate
    await authenticate(req, res, () => {
      // Check if user is an admin
      if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'super_admin' && !req.user.isMainAdmin)) {
        return res.status(403).json({
          success: false,
          message: 'Access denied',
          error: 'Admin privileges required'
        });
      }
      next();
    });
  } catch (error) {
    console.error('Admin authorization error:', error);
    res.status(401).json({
      success: false,
      message: 'Authentication required',
      error: 'Please authenticate as an admin'
    });
  }
};

/**
 * Main admin authorization middleware
 * Requires user to be authenticated as the main admin
 */
export const requireMainAdmin = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // First authenticate
    await authenticate(req, res, () => {
      // Check if user is the main admin
      if (!req.user || !req.user.isMainAdmin) {
        return res.status(403).json({
          success: false,
          message: 'Access denied',
          error: 'Main admin privileges required'
        });
      }
      next();
    });
  } catch (error) {
    console.error('Main admin authorization error:', error);
    res.status(401).json({
      success: false,
      message: 'Authentication required',
      error: 'Please authenticate as the main admin'
    });
  }
};

/**
 * Optional authentication middleware
 * Adds user info to request if authenticated, but doesn't require authentication
 */
export const optionalAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const token = req.cookies.auth_token;

    if (token) {
      try {
        const decoded = jwt.verify(
          token,
          process.env.JWT_SECRET || 'fallback-secret-key',
          {
            algorithms: ['HS256'],
            issuer: process.env.JWT_ISSUER || 'mabourse-api',
            audience: process.env.JWT_AUDIENCE || 'mabourse-admin',
            clockTolerance: 30,
            maxAge: '2h'
          }
        ) as any;

        const admin = await Admin.findById(decoded.id);
        if (admin && (!admin.lockUntil || admin.lockUntil <= new Date())) {
          req.user = {
            id: decoded.id,
            email: decoded.email,
            role: decoded.role,
            isMainAdmin: decoded.isMainAdmin || false
          };
        }
      } catch (error) {
        // Ignore authentication errors for optional auth
        console.log('Optional auth failed (this is normal):', (error as Error).message);
      }
    }

    next();
  } catch (error) {
    console.error('Optional auth middleware error:', error);
    next(); // Continue even if there's an error
  }
};
