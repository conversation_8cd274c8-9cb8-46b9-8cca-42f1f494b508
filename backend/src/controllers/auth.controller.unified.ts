import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import rateLimit from 'express-rate-limit';
import { Admin } from '../models/Admin';
import { SecurityMonitor } from '../utils/securityMonitor';
import { JWTUtils, JWT_CONFIG, COOKIE_CONFIG } from '../config/jwt.config';
import { AuthEventType, SecurityLevel } from '../utils/authLogger';

/**
 * UNIFIED ADMIN AUTHENTICATION CONTROLLER
 * Industry-standard implementation with HTTP-only cookies
 */

// Rate limiting configuration
export const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: {
    success: false,
    message: 'Too many login attempts',
    error: 'Please try again after 15 minutes',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true,
  keyGenerator: (req: Request) => req.ip || 'unknown'
});

/**
 * Admin Login - Industry Standard Implementation
 */
export const adminLogin = async (req: Request, res: Response) => {
  try {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { email, password } = req.body;
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';

    // Find admin by email
    const admin = await Admin.findByEmail(email);
    if (!admin) {
      // Log failed login attempt
      await SecurityMonitor.logLoginAttempt({
        email,
        ip,
        userAgent,
        success: false,
        failureReason: 'USER_NOT_FOUND'
      });

      return res.status(401).json({
        success: false,
        message: 'Invalid credentials',
        code: 'AUTHENTICATION_FAILED'
      });
    }

    // Check if account is locked
    if (admin.lockUntil && admin.lockUntil > new Date()) {
      return res.status(423).json({
        success: false,
        message: 'Account is locked',
        error: 'Please try again later or contact support',
        code: 'ACCOUNT_LOCKED'
      });
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, admin.password);
    if (!isPasswordValid) {
      // Increment failed login attempts
      const newAttempts = (admin.failedLoginAttempts || 0) + 1;
      const lockUntil = newAttempts >= 5 ? new Date(Date.now() + 30 * 60 * 1000) : undefined;

      await Admin.updateFailedLoginAttempts(admin.id!, newAttempts, lockUntil);

      // Log failed login attempt
      await SecurityMonitor.logLoginAttempt({
        email,
        ip,
        userAgent,
        success: false,
        failureReason: 'INVALID_PASSWORD'
      });

      return res.status(401).json({
        success: false,
        message: 'Invalid credentials',
        code: 'AUTHENTICATION_FAILED'
      });
    }

    // Reset failed login attempts on successful login
    if (admin.failedLoginAttempts > 0) {
      await Admin.updateFailedLoginAttempts(admin.id!, 0, undefined);
    }

    // Generate JWT token using centralized configuration
    const tokenPayload = {
      id: admin.id!,
      email: admin.email,
      role: admin.role,
      isMainAdmin: admin.isMainAdmin || false,
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent')?.substring(0, 100),
      sessionId: `session-${admin.id}-${Date.now()}`
    };

    const token = JWTUtils.generateToken(tokenPayload);

    // Set secure HTTP-only cookie using centralized configuration
    res.cookie(JWT_CONFIG.COOKIE_NAME, token, JWTUtils.getAccessTokenCookieOptions());

    // Log successful login
    await SecurityMonitor.logLoginAttempt({
      email,
      ip,
      userAgent,
      success: true
    });

    // Parse privileges
    const privileges = typeof admin.privileges === 'string' 
      ? JSON.parse(admin.privileges) 
      : admin.privileges || [];

    // Return success response
    res.json({
      success: true,
      message: 'Login successful',
      data: {
        admin: {
          id: admin.id,
          name: admin.name,
          email: admin.email,
          role: admin.role,
          isMainAdmin: admin.isMainAdmin || false,
          privileges,
          twoFactorEnabled: admin.twoFactorEnabled || false
        }
      }
    });

  } catch (error) {
    console.error('Admin login error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
};

/**
 * Admin Logout
 */
export const adminLogout = async (req: Request, res: Response) => {
  try {
    // Clear authentication cookie using centralized configuration
    res.clearCookie(JWT_CONFIG.COOKIE_NAME, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax',
      path: '/',
      domain: process.env.NODE_ENV === 'production' ? process.env.COOKIE_DOMAIN : undefined
    });

    res.json({
      success: true,
      message: 'Logout successful'
    });

  } catch (error) {
    console.error('Admin logout error:', error);
    res.status(500).json({
      success: false,
      message: 'Logout failed',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
};

/**
 * Get Current Admin Profile
 */
export const getCurrentAdmin = async (req: Request, res: Response) => {
  try {
    const adminId = (req as any).user?.id;
    
    if (!adminId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const admin = await Admin.findById(adminId);
    if (!admin) {
      return res.status(404).json({
        success: false,
        message: 'Admin not found'
      });
    }

    // Parse privileges
    const privileges = typeof admin.privileges === 'string' 
      ? JSON.parse(admin.privileges) 
      : admin.privileges || [];

    res.json({
      success: true,
      message: 'Admin profile retrieved successfully',
      data: {
        admin: {
          id: admin.id,
          name: admin.name,
          email: admin.email,
          role: admin.role,
          isMainAdmin: admin.isMainAdmin || false,
          privileges,
          twoFactorEnabled: admin.twoFactorEnabled || false
        }
      }
    });

  } catch (error) {
    console.error('Get admin profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get admin profile',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
};

/**
 * Verify Authentication Status
 */
export const verifyAuth = async (req: Request, res: Response) => {
  try {
    const user = (req as any).user;
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Not authenticated'
      });
    }

    res.json({
      success: true,
      message: 'Authentication verified',
      data: {
        authenticated: true,
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          isMainAdmin: user.isMainAdmin || false
        }
      }
    });

  } catch (error) {
    console.error('Verify auth error:', error);
    res.status(500).json({
      success: false,
      message: 'Authentication verification failed',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
};
